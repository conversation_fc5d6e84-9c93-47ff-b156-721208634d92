import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES UNIFIÉES - SYSTÈME COMPLET
import UnifiedStars from './UnifiedStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  intensity?: number; // 0-1, progression du lever de soleil
  autoStart?: boolean; // Démarrage automatique de la séquence
  onProgressionStart?: () => void; // Callback quand la progression démarre
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 60s)
  onTimerStart?: () => void; // Callback externe pour démarrer la progression
  showDevModal?: boolean; // Affiche la modale d'infos dev
  onCloseDevModal?: () => void; // Ferme la modale d'infos dev
}

// 🎯 CISCO: Interface pour contrôle externe
export interface ModeLeverSoleilRef {
  startProgression: () => void;
  stopProgression: () => void;
  getCurrentPhase: () => string;
}

/**
 * 🌅 MODULE LEVER DE SOLEIL - TOUT EN UN
 *
 * CISCO INSTRUCTIONS: Ce module contient TOUT pour le lever du soleil
 * ✅ DÉMARRAGE EN MODE NUIT (tout sombre, étoiles visibles)
 * ✅ SONS NUIT → LEVER DE SOLEIL (temporisation)
 * ✅ Éclairage global spécifique au lever
 * ✅ Position du soleil qui se lève
 * ✅ Quelques étoiles qui disparaissent progressivement
 * ✅ Éclairage du paysage qui s'éclaircit
 * ✅ Dégradé de couleurs aube (monte du bas vers le haut)
 * ✅ TEMPORISATION AUTOMATIQUE progressive
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  intensity = 0.0, // 🔧 CISCO: Démarrage à 0 (nuit complète)
  autoStart = false, // 🔧 CISCO: Pas de démarrage automatique par défaut
  onProgressionStart,
  timerDuration = 120, // Valeur par défaut 120s
  showDevModal = false,
  onCloseDevModal
}) => {
  // Timer dynamique : 10s en mode dev, sinon valeur reçue ou 120s par défaut
  // Timer fixe : 120s
  const effectiveTimerDuration = timerDuration;
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const moonRef = useRef<HTMLImageElement>(null); // 🌙 Ajout lune (corrigé pour <img>)
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const moonIntervalRef = useRef<NodeJS.Timeout | null>(null); // 🌙 CISCO: Référence pour éviter les conflits

  // 🌅 ÉTAT INTERNE POUR LA PROGRESSION AUTOMATIQUE
  const [currentIntensity, setCurrentIntensity] = useState(0.0); // Démarrage nuit complète
  // 🌙 Intensité indépendante pour la lune
  const [moonIntensity, setMoonIntensity] = useState(0.0);
  const [isProgressing, setIsProgressing] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<'nuit' | 'transition' | 'lever'>('nuit');

  // 🌅 PALETTES COULEURS NATURELLES - 4 PHASES DE 15 SECONDES (CISCO: recherche web)
  const COLOR_PHASES = {
    // Palette réaliste inspirée de photos de lever de soleil
    DEEP_NIGHT: {
      almostBlack: '#0B1426',    // Presque noir
      nightSky: '#041A40',       // Bleu nuit profond
      darkMidnight: '#001540',   // Bleu cétacé
      deepBlue: '#00224B',       // Bleu Oxford
    },

    LATE_NIGHT: {
      midnightBlue: '#0A1A3A',   // Bleu nuit plus doux
      indigo: '#1B2A4B',         // Indigo
      blueGrey: '#2C3E5C',       // Bleu-gris
      horizonBlue: '#3B4D6B',    // Bleu horizon
    },

    DAWN: {
      dawnBlue: '#4B5D7A',       // Bleu aube
      paleBlue: '#6A7CA3',       // Bleu pâle
      softGold: '#F6E7B4',       // Or très pâle
      horizonGold: '#FFE5A0',    // Jaune doré horizon
    },

    SUNRISE: {
      sunriseGold: '#FFE9B0',    // Jaune très pâle, presque blanc
      sunriseYellow: '#FFF8DC',  // Jaune pâle
      pastelRose: '#F7E6F7',     // Rose pastel très léger
      pastelOrange: '#FFE5C2',   // Orange pastel doux
      skyBlue: '#7BB8E7',        // Bleu pastel plus prononcé
      whiteBeige: '#FFFDF5',     // Blanc/beige soleil haut
    }
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
    sunrise: '/sounds/lever-soleil/Lever_soleil-nature.mp3',
    transitionDelay: 30000, // 30 secondes avant transition audio
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  };

  // 🌟 CISCO: ÉTOILES DÉPLACÉES DANS SimpleStars.tsx

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }

    audioRef.current = new Audio(AUDIO_CONFIG.night);
    audioRef.current.loop = true;
    audioRef.current.volume = 0.6;
    audioRef.current.play().catch(console.warn);
    console.log('🌙 Son de nuit démarré');
  };

  const transitionToSunriseSound = () => {
    if (!audioRef.current) return;

    // Fade out du son de nuit
    const fadeOutInterval = setInterval(() => {
      if (audioRef.current && audioRef.current.volume > 0.1) {
        audioRef.current.volume = Math.max(0, audioRef.current.volume - 0.1);
      } else {
        clearInterval(fadeOutInterval);
        if (audioRef.current) {
          audioRef.current.pause();
        }

        // Démarrer le son du lever de soleil
        audioRef.current = new Audio(AUDIO_CONFIG.sunrise);
        audioRef.current.loop = true;
        audioRef.current.volume = 0;
        audioRef.current.play().catch(console.warn);

        // Fade in du son du lever
        const fadeInInterval = setInterval(() => {
          if (audioRef.current && audioRef.current.volume < 0.5) {
            audioRef.current.volume = Math.min(0.5, audioRef.current.volume + 0.05);
          } else {
            clearInterval(fadeInInterval);
          }
        }, 100);

        console.log('🌅 Transition vers son du lever de soleil');
      }
    }, 100);
  };

  // 🌅 ANIMATION DU SOLEIL QUI SE LÈVE (CISCO: derrière le paysage)
  // Correction CISCO : le soleil monte progressivement APRÈS le coucher de la lune
  const sunAnimationStarted = useRef(false);
  const animateSunrise = (progressIntensity: number) => {
    if (!sunRef.current) return;
    let effectiveIntensity = Math.max(0, Math.min(progressIntensity, 1));
    const sunStartY = 250;
    const sunEndY = 25;
    let sunSize = 90;
    // Nouvelle logique de halo : lever chaud, transition vers blanc/beige
    // Utilisation de la palette COLOR_PHASES
    let sunHalo;
    if (effectiveIntensity < 0.15) {
      // Soleil juste au-dessus du paysage : halo chaud, contours lumineux, pas de gris
      sunHalo = `radial-gradient(circle, rgba(255,255,245,0.95) 0%, #FFD1A1 25%, #FFB07A 55%, rgba(255,180,120,0.25) 80%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 75;
    } else if (effectiveIntensity < 0.35) {
      // Phase chaude accentuée, éclat renforcé, transition plus lente
      sunHalo = `radial-gradient(circle, rgba(255,255,250,0.98) 0%, #FFE5C2 30%, #FFD1A1 60%, rgba(255,200,150,0.18) 85%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 90;
    } else if (effectiveIntensity < 0.7) {
      // Transition douce vers blanc/beige, contours lumineux
      sunHalo = `radial-gradient(circle, rgba(255,255,255,1) 0%, #FFFDF5 45%, #FFE9B0 75%, rgba(255,255,255,0.08) 95%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 105;
    } else {
      // Soleil au zénith : halo très éclatant, centre blanc pur, contours transparents
      sunHalo = `radial-gradient(circle, rgba(255,255,255,1) 0%, #FFFDF5 60%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 120;
    }
    // Ajout d'un délai de 5 secondes avant le lever du soleil
    // Ralentissement de la transition bleu/rose
  const delayThreshold = 22 / effectiveTimerDuration; // 22 secondes sur la durée totale (plus lent)
    if (effectiveIntensity < delayThreshold) {
      gsap.set(sunRef.current, {
        y: `${sunStartY}%`,
        opacity: 0,
        width: `${sunSize}px`,
        height: `${sunSize}px`,
        background: sunHalo
      });
      sunAnimationStarted.current = false;
      return;
    }

    // Démarre l'animation de montée une seule fois après le délai
    if (!sunAnimationStarted.current) {
      gsap.to(sunRef.current, {
        y: `${sunEndY}%`,
        opacity: 1,
        width: `${sunSize}px`,
        height: `${sunSize}px`,
        background: sunHalo,
        duration: 50.0, // Encore plus lent
        ease: "power1.inOut"
      });
      sunAnimationStarted.current = true;
    } else {
      // Mise à jour de la couleur et taille du halo pendant la progression
      sunRef.current.style.background = sunHalo;
      sunRef.current.style.width = `${sunSize}px`;
      sunRef.current.style.height = `${sunSize}px`;
    }
  };

  // 🌙 ANIMATION DE LA LUNE QUI SE COUCHE - MODE INDÉPENDANT
  const animateMoonset = (moonProgressIntensity: number) => {
    if (!moonRef.current) return;

    // 🌙 CISCO: MOUVEMENT INDÉPENDANT DE LA LUNE - TRAJECTOIRE COMPLÈTE
    // La lune suit sa propre trajectoire, complètement découplée du soleil

    // 📍 POSITIONS VERTICALES (Y) - De haut en bas
    const moonStartY = 120;  // DÉPART : Position haute (visible dans le ciel)
    const moonEndY = 1200;   // ARRIVÉE : Position finale (TRÈS bas, complètement hors écran)

    // 🧮 CALCUL POSITION Y ACTUELLE
    let currentMoonY;
    if (moonProgressIntensity >= 1.0) {
      currentMoonY = moonEndY; // Position finale garantie
    } else {
      // Interpolation linéaire : 120% → 1200% selon progression (0 → 1)
      currentMoonY = moonStartY + (moonProgressIntensity * (moonEndY - moonStartY));
    }

    // 📍 POSITIONS HORIZONTALES (X) - Trajectoire en arc
    const moonStartLeft = 10;   // DÉPART : Commence à gauche (10%)
    const moonEndLeft = 150;    // ARRIVÉE : Finit plus à droite (150%)

    // 🧮 CALCUL POSITION X ACTUELLE (CORRIGÉ)
    const currentMoonLeft = moonStartLeft + (moonProgressIntensity * (moonEndLeft - moonStartLeft));

    // 👁️ OPACITÉ - La lune reste visible pendant tout son parcours
    const moonOpacity = 1.0;

    // 🎬 ANIMATION GSAP ULTRA-FLUIDE - Paramètres détaillés
    gsap.to(moonRef.current, {
      y: `${currentMoonY}%`,        // POSITION Y : de 120% à 1200% (descente)
      left: `${currentMoonLeft}%`,  // POSITION X : de 10% à 150% (arc horizontal)
      opacity: moonOpacity,         // OPACITÉ : toujours visible (1.0)
      width: '140px',               // TAILLE : constante 140px
      height: '140px',              // TAILLE : constante 140px
      duration: 0.1,                // DURÉE : 0.1s transition douce entre positions
      ease: "none",                 // COURBE : linéaire (pas d'accélération/décélération)
      overwrite: true               // SÉCURITÉ : évite les conflits d'animation
    });

    // 🔍 CISCO: Debug position actuelle
    console.log(`🌙 Position: Y=${currentMoonY.toFixed(1)}% X=${currentMoonLeft.toFixed(1)}% Progress=${(moonProgressIntensity*100).toFixed(1)}%`);
  };

  // ⭐ ANIMATION DES ÉTOILES QUI DISPARAISSENT (CISCO: progressif naturel)
  const animateStarsFading = (progressIntensity: number) => {
    if (!starsContainerRef.current) return;
    // Disparition encore plus progressive et lente
    let starsOpacity;
    if (progressIntensity < 0.2) {
      starsOpacity = 1.0;
    } else if (progressIntensity < 0.85) {
      // Disparition très étalée sur la fin
      starsOpacity = 1.0 - ((progressIntensity - 0.2) / 0.65);
    } else {
      starsOpacity = 0;
    }
    gsap.to(starsContainerRef.current, {
      opacity: Math.max(starsOpacity, 0),
      duration: 6.0, // Encore plus lent pour effet naturel
      ease: "power2.out"
    });
  };

  // 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil)
  const animateGlobalLighting = (progressIntensity: number) => {
    if (!globalLightRef.current) return;

    // Synchronisation stricte avec le lever du soleil
    // Avant le lever, lumière très faible
  const delayThreshold = 15 / effectiveTimerDuration;
    let lightIntensity;
    if (progressIntensity < delayThreshold) {
      lightIntensity = 0;
    } else {
      // Synchronisation : la lumière augmente en même temps que le soleil
      const sunProgress = (progressIntensity - delayThreshold) / (1 - delayThreshold);
      lightIntensity = Math.max(0, Math.min(sunProgress, 1));
    }
    gsap.to(globalLightRef.current, {
      opacity: lightIntensity,
      duration: 3.0,
      ease: "power1.inOut"
    });
  };

  // 🏔️ ÉCLAIRAGE DU PAYSAGE (CISCO: s'éclaircit progressivement)
  const animateLandscapeLighting = (progressIntensity: number) => {
    if (!landscapeRef.current) return;

    // CISCO: Le paysage s'éclaircit avec le lever du soleil
    let brightness;
    if (progressIntensity < 0.1) {
      brightness = 0.15; // Très sombre au début (nuit)
    } else if (progressIntensity < 0.7) {
      brightness = 0.15 + (progressIntensity - 0.1) * 1.0; // Éclaircissement progressif
    } else {
      brightness = 0.75 + (progressIntensity - 0.7) * 0.5; // Pleine lumière
    }

    gsap.to(landscapeRef.current, {
      filter: `brightness(${brightness})`,
      duration: 3.5,
      ease: "power1.inOut"
    });
  };

  // 🎨 DÉGRADÉ DYNAMIQUE 4 PHASES - 60 SECONDES (CISCO: système temporisé)
  const createSunriseGradient = (progressIntensity: number) => {
    if (!containerRef.current) return;
    let gradient;
    let stops;
    if (progressIntensity < 0.25) {
      // Nuit profonde, haut très sombre
      stops = [
        '#000010',
        COLOR_PHASES.DEEP_NIGHT.darkMidnight,
        COLOR_PHASES.DEEP_NIGHT.nightSky,
        COLOR_PHASES.DEEP_NIGHT.almostBlack
      ];
      gradient = `linear-gradient(to top, ${stops[3]} 0%, ${stops[2]} 40%, ${stops[1]} 80%, ${stops[0]} 100%)`;
    } else if (progressIntensity < 0.5) {
      // Transition nuit → aube
      stops = [
        COLOR_PHASES.LATE_NIGHT.horizonBlue,
        COLOR_PHASES.LATE_NIGHT.blueGrey,
        COLOR_PHASES.LATE_NIGHT.indigo,
        COLOR_PHASES.LATE_NIGHT.midnightBlue
      ];
      gradient = `linear-gradient(to top, ${stops[3]} 0%, ${stops[2]} 40%, ${stops[1]} 80%, ${stops[0]} 100%)`;
    } else if (progressIntensity < 0.75) {
      // Aube, le bas du ciel s'éclaircit
      stops = [
        COLOR_PHASES.DAWN.horizonGold,
        COLOR_PHASES.DAWN.softGold,
        COLOR_PHASES.DAWN.paleBlue,
        COLOR_PHASES.DAWN.dawnBlue
      ];
      gradient = `linear-gradient(to top, ${stops[3]} 0%, ${stops[2]} 40%, ${stops[1]} 80%, ${stops[0]} 100%)`;
    } else {
      // Lever de soleil final : bleu pastel proéminent en haut, rose très fin sur l'horizon, jaune très doux
      stops = [
        COLOR_PHASES.SUNRISE.sunriseGold,      // bas (jaune très pâle)
        COLOR_PHASES.SUNRISE.pastelOrange,     // reflet orange pastel
        COLOR_PHASES.SUNRISE.pastelRose,       // rose pastel très fin
        COLOR_PHASES.SUNRISE.skyBlue           // haut bleu pastel plus prononcé
      ];
      // Accentuer le bleu, estomper le rose plus bas
      gradient = `linear-gradient(to top, ${stops[0]} 0%, ${stops[1]} 8%, ${stops[2]} 14%, ${stops[3]} 100%)`;
    }
    // Animation du dégradé si progression en cours
    if (isProgressing) {
      const duration = progressIntensity >= 0.75 ? 6.0 : 3.5; // Ralentissement fort en dernière phase
      gsap.to(containerRef.current, {
        backgroundImage: gradient,
        duration,
        ease: "power1.inOut"
      });
    } else {
      containerRef.current.style.backgroundImage = gradient;
    }
  };

  // 🎬 ORCHESTRATION COMPLÈTE DU LEVER DE SOLEIL (CISCO: tout synchronisé)
  const orchestrateSunrise = (progressIntensity: number) => {
    console.log(`🌅 CISCO Orchestration lever de soleil - Intensité: ${progressIntensity.toFixed(2)} - Phase: ${currentPhase}`);

    // Tuer l'animation précédente si elle existe
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Créer une nouvelle timeline synchronisée
    timelineRef.current = gsap.timeline();

    // CISCO: Lancer toutes les animations en parallèle - TOUT synchronisé
  createSunriseGradient(progressIntensity);
  animateSunrise(progressIntensity);
  // 🌙 CISCO: La lune est animée INDÉPENDAMMENT avec sa propre intensité
  // Note: animateMoonset est appelée séparément dans useEffect avec moonIntensity
  animateStarsFading(progressIntensity);
  animateGlobalLighting(progressIntensity);
  animateLandscapeLighting(progressIntensity);

    // CISCO: Gestion des phases et transitions audio
    if (progressIntensity < 0.1 && currentPhase !== 'nuit') {
      setCurrentPhase('nuit');
    } else if (progressIntensity >= 0.1 && progressIntensity < 0.6 && currentPhase !== 'transition') {
      const newPhase = 'transition';
      setCurrentPhase(newPhase);
      // Transition audio après 30 secondes
      setTimeout(() => {
        // Vérifier que nous sommes toujours en phase de transition
        setCurrentPhase(prevPhase => {
          if (prevPhase === 'transition') {
            transitionToSunriseSound();
          }
          return prevPhase;
        });
      }, AUDIO_CONFIG.transitionDelay);
    } else if (progressIntensity >= 0.6 && currentPhase !== 'lever') {
      setCurrentPhase('lever');
    }
  };

  // 🕐 TEMPORISATION AUTOMATIQUE (CISCO: progression basée sur durée temporisateur)
  const startAutomaticProgression = () => {
    if (isProgressing) return;

  console.log(`🌅 CISCO: Démarrage progression automatique du lever de soleil (${effectiveTimerDuration}s)`);
    setIsProgressing(true);
    setCurrentIntensity(0.0); // Démarrage nuit complète

    // Démarrer le son de nuit
    playNightSound();

    // Callback pour notifier le démarrage
    if (onProgressionStart) {
      onProgressionStart();
    }

    // 🔧 CISCO: Progression automatique EXACTEMENT 60 secondes - 4 phases de 15s
    const totalDuration = 60 * 1000; // 60 secondes en millisecondes
    const updateInterval = 250; // Mise à jour toutes les 250ms pour fluidité
    const incrementPerUpdate = 1.0 / (totalDuration / updateInterval); // 0.004167 par update

    const progressionInterval = setInterval(() => {
      setCurrentIntensity(prev => {
        const newIntensity = prev + incrementPerUpdate;
  console.log(`[SYNC] Progression lune/soleil : intensity=${newIntensity.toFixed(3)} / timerDuration=${effectiveTimerDuration}`);
        if (newIntensity >= 1.0) {
          clearInterval(progressionInterval);
          setIsProgressing(false);
          console.log('🌅 CISCO: Lever de soleil terminé');
          return 1.0;
        }
        return newIntensity;
      });
    }, updateInterval); // 250ms pour fluidité

    // Nettoyer l'intervalle si le composant se démonte
    return () => clearInterval(progressionInterval);
  };

  // 🎯 CISCO: Méthodes publiques pour contrôle externe
  const startProgression = () => {
    if (!isProgressing) {
      console.log('🌅 CISCO: Démarrage progression externe (temporisateur)');
      startAutomaticProgression();
    }
  };

  const stopProgression = () => {
    console.log('🌅 CISCO: Arrêt de la progression du lever de soleil');
    setIsProgressing(false);

    // Arrêter l'audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Remettre en mode nuit
    setCurrentIntensity(0.0);
    setCurrentPhase('nuit');
    orchestrateSunrise(0.0);
  };

  const getCurrentPhase = () => {
    if (currentIntensity < 0.25) return 'Nuit profonde';
    if (currentIntensity < 0.5) return 'Fin de nuit';
    if (currentIntensity < 0.75) return 'Aube';
    return 'Lever de soleil';
  };

  // 🔧 CISCO: Exposer les méthodes pour contrôle externe via window
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).sunriseControls = {
        start: startProgression,
        stop: stopProgression,
        getPhase: getCurrentPhase,
        getIntensity: () => currentIntensity
      };
    }
  }, [currentIntensity, isProgressing]);

  // 🔄 EFFET PRINCIPAL - RÉACTION AUX CHANGEMENTS (CISCO: démarrage en mode nuit)
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Démarrage nuit complète');

      // 🔧 CISCO: FORCER IMMÉDIATEMENT LE MODE NUIT - Éviter le flash
      if (containerRef.current) {
        // Appliquer immédiatement le dégradé nuit avec haut très sombre
        containerRef.current.style.backgroundImage = `linear-gradient(to top,
          ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 0%,
          ${COLOR_PHASES.DEEP_NIGHT.nightSky} 40%,
          ${COLOR_PHASES.DEEP_NIGHT.darkMidnight} 70%,
          #000010 100%)`;
      }

      // CISCO: TOUJOURS démarrer en mode nuit (intensité 0) - PAS de progression automatique
      // La progression ne se lance que quand le temporisateur démarre
      orchestrateSunrise(0.0); // Force le mode nuit au démarrage

      // CISCO: Démarrage automatique SEULEMENT si explicitement demandé
      if (autoStart && !isProgressing) {
        console.log('🌅 CISCO: AutoStart activé - Lancement progression automatique');
        startAutomaticProgression();
      }
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');

      // Arrêter la progression automatique
      setIsProgressing(false);

      // Arrêter l'audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // 🔧 CISCO: SUPPRESSION FADE OUT - Application immédiate pour éviter dégradé
      if (containerRef.current) {
        containerRef.current.style.opacity = '0';
      }
    }

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [isActive, autoStart]); // Suppression de 'intensity' pour éviter les re-renders

  // 🔄 EFFET POUR LA PROGRESSION AUTOMATIQUE
  useEffect(() => {
    // Orchestration appelée uniquement au démarrage de la progression
    if (isProgressing && isActive && currentIntensity === 0) {
      orchestrateSunrise(currentIntensity);
    }
    // Réinitialise le drapeau si on revient à la nuit
    if (currentIntensity < 0.25) {
      sunAnimationStarted.current = false;
    }
  }, [isProgressing, isActive, currentIntensity]);

  // 🔄 EFFET POUR LA PROGRESSION INDÉPENDANTE DE LA LUNE
  useEffect(() => {
    // 🚨 CISCO: PROTECTION CONTRE LES REDÉMARRAGES INTEMPESTIFS
    if (isProgressing && isActive && !moonIntervalRef.current) {
      // Vitesse lune : accélérée (40s)
      // 🌙 CISCO: CONTRÔLE DE VITESSE INDÉPENDANT DE LA LUNE
      const moonSpeedFactor = 400; // VITESSE : 400 secondes = très lente et majestueuse
      const moonDuration = moonSpeedFactor * 1000; // DURÉE TOTALE : convertit en millisecondes (400s = 400000ms)
      const updateInterval = 50; // FRÉQUENCE : mise à jour toutes les 50ms pour fluidité maximale
      const incrementPerUpdate = 1.0 / (moonDuration / updateInterval); // PROGRESSION : petit pas à chaque update (1/8000 = 0.000125 par update)
      // 🌙 CISCO: Réinitialisation SEULEMENT au début d'une nouvelle progression
      setMoonIntensity(0.0);
      console.log(`🌙 CISCO: Démarrage mouvement indépendant de la lune (${moonSpeedFactor}s)`);

      moonIntervalRef.current = setInterval(() => {
        setMoonIntensity(prev => {
          const newIntensity = prev + incrementPerUpdate;
          // 🔍 CISCO: Debug - affiche la progression toutes les 5 secondes
          if (Math.floor(newIntensity * 100) % 5 === 0 && Math.floor(prev * 100) !== Math.floor(newIntensity * 100)) {
            console.log(`🌙 Lune progression: ${(newIntensity * 100).toFixed(1)}% - Position Y: ${120 + (newIntensity * (1200 - 120))}%`);
          }
          if (newIntensity >= 1.0) {
            if (moonIntervalRef.current) {
              clearInterval(moonIntervalRef.current);
              moonIntervalRef.current = null;
            }
            console.log('🌙 CISCO: Lune terminée - position finale atteinte');
            return 1.0;
          }
          return newIntensity;
        });
      }, updateInterval);
      return () => {
        if (moonIntervalRef.current) {
          clearInterval(moonIntervalRef.current);
          moonIntervalRef.current = null;
        }
      };
    }
    // 🌙 CISCO: PAS de réinitialisation automatique - la lune reste en position finale
  }, [isProgressing, isActive]);

  // Animation fluide de la lune à chaque tick
  useEffect(() => {
    animateMoonset(moonIntensity);
  }, [moonIntensity]);

  // 🌟 CISCO: ÉTOILES GÉRÉES PAR SimpleStars.tsx

  // Mini-box modale d'infos dev (toujours visible en haut à gauche)

  const elapsedSeconds = Math.floor(currentIntensity * effectiveTimerDuration);
  let degradeMode = 'Nuit';
  let phaseIndex = 1;
  let phaseName = 'Première phase : Nuit';
  let phaseTimes = '0s - 15s';
  if (currentIntensity < 0.25) {
    degradeMode = 'Nuit';
    phaseIndex = 1;
    phaseName = 'Première phase : Nuit';
    phaseTimes = '0s - 15s';
  } else if (currentIntensity < 0.5) {
    degradeMode = 'Transition';
    phaseIndex = 2;
    phaseName = 'Deuxième phase : Transition';
    phaseTimes = '15s - 30s';
  } else if (currentIntensity < 0.85) {
    degradeMode = 'Aube';
    phaseIndex = 3;
    phaseName = 'Troisième phase : Aube';
    phaseTimes = '30s - 51s';
  } else {
    degradeMode = 'Lever du soleil';
    phaseIndex = 4;
    phaseName = 'Quatrième phase : Lever du soleil';
    phaseTimes = '51s - 60s';
  }

  // Éclairage (simulé) - Utiliser la prop intensity si fournie, sinon l'état interne
  const effectiveIntensity = intensity !== undefined ? intensity : currentIntensity;
  const nuagesLight = degradeMode === 'Nuit' ? 'très sombre' : degradeMode === 'Transition' ? 'bleu nuit' : degradeMode === 'Aube' ? 'rosé/doré' : 'jaune/blanc';
  const globalLight = (effectiveIntensity * 100 < 20) ? 'très faible' : (effectiveIntensity * 100 < 50) ? 'progressif' : (effectiveIntensity * 100 < 85) ? 'lumineux' : 'très lumineux';
  const paysageLight = degradeMode === 'Nuit' ? 'sombre' : degradeMode === 'Transition' ? 'bleu-gris' : degradeMode === 'Aube' ? 'doré' : 'clair';

  // Progression en %
  const progressionPercent = Math.round(effectiveIntensity * 100);
  // Soleil : position Y (simulée)
  const sunY = 250 - (225 * effectiveIntensity);
  // Lune : position Y (simulée)
  const moonY = 30 + (130 * effectiveIntensity);
  // Opacité étoiles
  const starsOpacity = effectiveIntensity < 0.6 ? (1 - effectiveIntensity * 1.5) : 0;

  return (
    <>
      {showDevModal && (
        <div style={{position:'fixed',top:16,left:16,zIndex:100000,background:'#222d',color:'#fff',padding:'12px 18px',borderRadius:'12px',fontSize:'1em',boxShadow:'0 2px 12px #0008',minWidth:260}}>
          <div style={{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:4}}>
            <div style={{fontWeight:'bold'}}>🛠️ Infos Animation Lever de Soleil</div>
            <button 
              onClick={onCloseDevModal}
              style={{background:'none',border:'none',color:'#fff',cursor:'pointer',fontSize:'16px',padding:'0 4px'}}
            >
              ✕
            </button>
          </div>
          <div><b>Mode dégradé :</b> {degradeMode}</div>
          <div><b>Phase active :</b> {phaseName} ({phaseTimes})</div>
          <div><b>Éclairage nuages :</b> {nuagesLight}</div>
          <div><b>Éclairage global :</b> {globalLight}</div>
          <div><b>Éclairage paysage :</b> {paysageLight}</div>
          <div><b>Temps écoulé :</b> {elapsedSeconds}s</div>
          <div><b>Phase :</b> {currentPhase}</div>
          <div><b>Progression :</b> {isProgressing ? 'OUI' : 'NON'} ({progressionPercent}%)</div>
          <div><b>Soleil Y :</b> {Math.round(sunY)}%</div>
          <div><b>Lune Y :</b> {Math.round(moonY)}%</div>
          <div><b>Opacité étoiles :</b> {starsOpacity.toFixed(2)}</div>
        </div>
      )}
      <div
        ref={containerRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: 5,
          opacity: isActive ? 1 : 0,
          backgroundImage: `linear-gradient(to top,
            ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 0%,
            ${COLOR_PHASES.DEEP_NIGHT.nightSky} 50%,
            ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 100%)`
        }}
      >
      {/* 🌙 LUNE AVEC HALO DISCRET (CISCO: descend très bas, mouvement indépendant) */}
      <img
        ref={moonRef}
        src="/Lune-Moon.png"
        alt="Lune"
        className="absolute"
        style={{
          width: '140px',
          height: '140px',
          objectFit: 'contain',
          opacity: 1,
          zIndex: 2,
          pointerEvents: 'none',
          userSelect: 'none',
          transition: 'width 0.3s, height 0.3s',
          // 🌙 CISCO: Halo lunaire plus grand pour impression d'éclairage
          filter: 'drop-shadow(0 0 35px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 70px rgba(220, 220, 255, 0.3)) drop-shadow(0 0 120px rgba(200, 200, 255, 0.15))',
          borderRadius: '50%'
        }}
      />

      {/* 🌅 SOLEIL LEVANT (CISCO: derrière le paysage) */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
    left: '75%',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    width: '90px',
    height: '90px',
          borderRadius: '50%',
          background: `radial-gradient(circle, rgba(255,255,255,0.95) 0%, #FFFDF5 60%, rgba(255,255,255,0.01) 100%)`,
          boxShadow: `0 0 120px 60px #FFFDF5, 0 0 200px 80px #FFFDF5, 0 0 40px 10px #FFFDF5`,
          opacity: 1,
    zIndex: 3,
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      />

      {/* ⭐ ÉTOILES UNIFIÉES - SYSTÈME COMPLET (CISCO: 300+ étoiles) */}
      <UnifiedStars
        skyMode="leverSoleil"
        isVisible={isActive}
        opacity={effectiveIntensity < 0.6 ? (1 - effectiveIntensity * 1.5) : 0}
      />

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil) */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            ${COLOR_PHASES.SUNRISE.sunriseGold}15 0%,
            ${COLOR_PHASES.SUNRISE.sunriseYellow}08 30%,
            ${COLOR_PHASES.SUNRISE.whiteBeige}05 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      {/* 🏔️ RÉFÉRENCE PAYSAGE POUR ÉCLAIRAGE (CISCO: s'éclaircit progressivement) */}
      <div
        ref={landscapeRef}
        className="absolute inset-0"
        style={{
          background: 'transparent',
          filter: 'brightness(0.15)', // Démarrage très sombre
          zIndex: 7
        }}
      />

      </div>
    </>
  );
};

export default ModeLeverSoleil;
