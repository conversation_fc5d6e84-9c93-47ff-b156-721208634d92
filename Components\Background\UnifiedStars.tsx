import React, { useEffect, useRef } from 'react';

interface UnifiedStarsProps {
  skyMode: string;
  isVisible: boolean;
  opacity: number; // 0-1 pour la disparition progressive
}

interface Star {
  id: number;
  x: number;
  y: number;
  size: number;
  opacity: number;
  color: string;
  type: 'big' | 'medium' | 'small';
  twinkleDuration: number;
  twinkleDelay: number;
}

/**
 * 🌟 SYSTÈME D'ÉTOILES UNIFIÉ - CISCO
 * 
 * Remplace tous les autres composants d'étoiles (SimpleStars, NewStars, FixedStars)
 * - BEAUCOUP plus d'étoiles (300+ au lieu de 15)
 * - 3 types d'étoiles : grosses, moyennes, petites
 * - Scintillement naturel et désynchronisé
 * - Z-index 9999 garanti au-dessus de tout
 * - Positions fixes (pas de mouvement)
 */
const UnifiedStars: React.FC<UnifiedStarsProps> = ({ skyMode, isVisible, opacity }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const starsRef = useRef<Star[]>([]);

  // 🌟 CONFIGURATION ÉTOILES SELON MODE
  const getStarConfig = (mode: string) => {
    switch (mode) {
      case 'night':
      case 'leverSoleil':
        return {
          big: 25,      // Grosses étoiles brillantes
          medium: 80,   // Étoiles moyennes
          small: 200    // Petites étoiles (poussière d'étoiles)
        }; // Total: 305 étoiles
      case 'dawn':
      case 'dusk':
        return {
          big: 15,      // Moins d'étoiles visibles
          medium: 50,   
          small: 100    
        }; // Total: 165 étoiles
      default:
        return { big: 0, medium: 0, small: 0 }; // Pas d'étoiles en journée
    }
  };

  // 🌟 CRÉATION D'ÉTOILES PAR TYPE
  const createStar = (id: number, type: 'big' | 'medium' | 'small'): Star => {
    let size, opacityRange, colors;

    switch (type) {
      case 'big':
        size = 3.5 + Math.random() * 2; // 3.5-5.5px
        opacityRange = [0.8, 0.9, 1.0];
        colors = [
          'rgba(255, 255, 255, 1)',     // Blanc pur
          'rgba(255, 255, 240, 1)',     // Blanc chaud
          'rgba(255, 250, 205, 1)',     // Blanc doré
          'rgba(240, 248, 255, 1)'      // Blanc bleuté
        ];
        break;
      case 'medium':
        size = 2 + Math.random() * 1.5; // 2-3.5px
        opacityRange = [0.6, 0.7, 0.8];
        colors = [
          'rgba(255, 255, 255, 0.9)',
          'rgba(255, 255, 240, 0.9)',
          'rgba(240, 248, 255, 0.9)'
        ];
        break;
      case 'small':
        size = 0.8 + Math.random() * 1.2; // 0.8-2px
        opacityRange = [0.3, 0.4, 0.5, 0.6];
        colors = [
          'rgba(255, 255, 255, 0.7)',
          'rgba(240, 248, 255, 0.7)',
          'rgba(230, 230, 250, 0.7)'
        ];
        break;
    }

    return {
      id,
      x: Math.random() * 100,
      y: Math.random() * 70, // Éviter le bas de l'écran (paysage)
      size,
      opacity: opacityRange[Math.floor(Math.random() * opacityRange.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      type,
      twinkleDuration: type === 'big' ? 4 + Math.random() * 3 : 
                      type === 'medium' ? 2 + Math.random() * 2 : 
                      1 + Math.random() * 1.5,
      twinkleDelay: Math.random() * 5 // Délai aléatoire pour désynchroniser
    };
  };

  // 🌟 GÉNÉRATION DE TOUTES LES ÉTOILES
  const generateStars = (mode: string): Star[] => {
    const config = getStarConfig(mode);
    const stars: Star[] = [];
    let id = 0;

    // Créer les grosses étoiles
    for (let i = 0; i < config.big; i++) {
      stars.push(createStar(id++, 'big'));
    }

    // Créer les étoiles moyennes
    for (let i = 0; i < config.medium; i++) {
      stars.push(createStar(id++, 'medium'));
    }

    // Créer les petites étoiles
    for (let i = 0; i < config.small; i++) {
      stars.push(createStar(id++, 'small'));
    }

    console.log(`🌟 UNIFIED STARS: ${stars.length} étoiles générées (${config.big} grosses + ${config.medium} moyennes + ${config.small} petites) pour mode ${mode}`);
    return stars;
  };

  // 🌟 RENDU DES ÉTOILES DANS LE DOM
  const renderStars = () => {
    if (!containerRef.current) return;

    // Nettoyer les anciennes étoiles
    containerRef.current.innerHTML = '';

    const stars = generateStars(skyMode);
    starsRef.current = stars;

    if (stars.length === 0) return;

    // Créer chaque étoile dans le DOM
    stars.forEach((star) => {
      const element = document.createElement('div');
      element.className = `unified-star star-${star.type}`;
      element.id = `unified-star-${star.id}`;

      element.style.cssText = `
        position: absolute;
        left: ${star.x}%;
        top: ${star.y}%;
        width: ${star.size}px;
        height: ${star.size}px;
        background: ${star.color};
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        opacity: 0;
        box-shadow: 0 0 ${star.size * 2}px ${star.color};
        animation: unifiedTwinkle-${star.type} ${star.twinkleDuration}s ease-in-out infinite alternate;
        animation-delay: ${star.twinkleDelay}s;
        transition: opacity 2s ease-in-out;
        will-change: opacity, transform;
      `;

      containerRef.current!.appendChild(element);
    });

    // Ajouter les animations CSS
    addStarAnimations();

    // Rendre les étoiles visibles progressivement
    setTimeout(() => {
      if (containerRef.current && isVisible) {
        const starElements = containerRef.current.querySelectorAll('.unified-star');
        starElements.forEach((element: Element, index: number) => {
          const htmlElement = element as HTMLElement;
          const star = starsRef.current[index];
          if (star) {
            // Délai progressif pour apparition naturelle
            const appearDelay = Math.random() * 3000; // 0-3s
            setTimeout(() => {
              htmlElement.style.opacity = (star.opacity * opacity).toString();
            }, appearDelay);
          }
        });
      }
    }, 100);
  };

  // 🌟 ANIMATIONS CSS POUR SCINTILLEMENT
  const addStarAnimations = () => {
    if (document.getElementById('unified-stars-animations')) return;

    const style = document.createElement('style');
    style.id = 'unified-stars-animations';
    style.textContent = `
      @keyframes unifiedTwinkle-big {
        0% { opacity: 0.6; transform: scale(0.9); filter: brightness(0.8); }
        50% { opacity: 0.9; transform: scale(1.0); filter: brightness(1.0); }
        100% { opacity: 1.0; transform: scale(1.1); filter: brightness(1.2); }
      }

      @keyframes unifiedTwinkle-medium {
        0% { opacity: 0.4; transform: scale(0.8); filter: brightness(0.7); }
        50% { opacity: 0.7; transform: scale(1.0); filter: brightness(1.0); }
        100% { opacity: 0.8; transform: scale(1.1); filter: brightness(1.1); }
      }

      @keyframes unifiedTwinkle-small {
        0% { opacity: 0.2; transform: scale(0.6); filter: brightness(0.6); }
        25% { opacity: 0.4; transform: scale(0.8); filter: brightness(0.8); }
        75% { opacity: 0.6; transform: scale(1.0); filter: brightness(1.0); }
        100% { opacity: 0.7; transform: scale(1.2); filter: brightness(1.2); }
      }
    `;
    document.head.appendChild(style);
  };

  // 🌟 MISE À JOUR DE L'OPACITÉ
  useEffect(() => {
    if (!containerRef.current) return;

    const starElements = containerRef.current.querySelectorAll('.unified-star');
    starElements.forEach((element: Element, index: number) => {
      const htmlElement = element as HTMLElement;
      const star = starsRef.current[index];
      if (star) {
        htmlElement.style.opacity = isVisible ? (star.opacity * opacity).toString() : '0';
      }
    });
  }, [isVisible, opacity]);

  // 🌟 RÉGÉNÉRATION SELON LE MODE
  useEffect(() => {
    renderStars();
  }, [skyMode]);

  // 🌟 NETTOYAGE
  useEffect(() => {
    return () => {
      const style = document.getElementById('unified-stars-animations');
      if (style) {
        style.remove();
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 overflow-hidden pointer-events-none"
      style={{
        zIndex: 9999, // Au-dessus de tout
        background: 'transparent'
      }}
    />
  );
};

export default UnifiedStars;
